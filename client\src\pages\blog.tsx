import React, { useState } from 'react';
import { Link } from 'wouter';
import { ArrowLeft, Calendar, User, ArrowRight, Clock, Eye, Search } from 'lucide-react';
import SEOHead from '../components/seo-head';
import BlogSearch from '../components/blog-search';
import Pagination from '../components/pagination';
import { useBlogPosts, useFeaturedBlogPosts, useBlogPostsCount } from '@/hooks/use-blog';
import { formatDistanceToNow } from 'date-fns';

export default function Blog() {
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 6;
  const offset = (currentPage - 1) * postsPerPage;

  const { data: featuredPosts, isLoading: featuredLoading } = useFeaturedBlogPosts(3);
  const { data: recentPosts, isLoading: recentLoading } = useBlogPosts(undefined, postsPerPage, offset);
  const { data: totalPosts } = useBlogPostsCount();

  const totalPages = Math.ceil((totalPosts || 0) / postsPerPage);
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <SEOHead
        title="Startup Ideas Blog | IdeaHunter"
        description="Discover insights, trends, and strategies for finding and validating startup ideas. Learn from real Reddit data and successful entrepreneurs."
        keywords={["startup blog", "business ideas", "entrepreneurship", "startup validation", "AI trends", "SaaS opportunities"]}
        url="https://ideahunter.today/blog"
      />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-cyan-400 hover:text-cyan-300 mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>

          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">IdeaHunter Blog</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-6">
              Insights, trends, and strategies for discovering and validating startup opportunities
            </p>

            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <BlogSearch />
            </div>
          </div>
        </div>

        {/* Featured Posts */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Featured Articles</h2>
          {featuredLoading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 animate-pulse">
                  <div className="h-6 bg-gray-700 rounded mb-4 w-20"></div>
                  <div className="h-6 bg-gray-700 rounded mb-3"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded mb-4 w-3/4"></div>
                  <div className="h-4 bg-gray-700 rounded w-24"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredPosts?.map((post) => (
                <article key={post.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:bg-gray-800 hover:border-gray-600 transition-all duration-200">
                  <div className="mb-4">
                    <span className="inline-block bg-cyan-600 text-white text-xs px-2 py-1 rounded">
                      {post.category}
                    </span>
                  </div>

                  <h3 className="text-xl font-semibold mb-3 line-clamp-2">
                    <Link
                      href={`/blog/${post.slug}`}
                      className="hover:text-cyan-400 transition-colors"
                    >
                      {post.title}
                    </Link>
                  </h3>

                  <p className="text-gray-300 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-400">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.read_time} min
                      </span>
                      <span className="flex items-center">
                        <Eye className="w-4 h-4 mr-1" />
                        {post.view_count}
                      </span>
                    </div>
                    <span>{formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}</span>
                  </div>
                </article>
              ))}
            </div>
          )}
        </section>

        {/* Recent Posts */}
        <section>
          <h2 className="text-2xl font-bold mb-6">Recent Posts</h2>
          {recentLoading ? (
            <div className="space-y-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 animate-pulse">
                  <div className="flex justify-between mb-4">
                    <div className="h-6 bg-gray-700 rounded w-20"></div>
                    <div className="h-4 bg-gray-700 rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-gray-700 rounded mb-3"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {recentPosts?.map((post) => (
                <article key={post.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:bg-gray-800 hover:border-gray-600 transition-all duration-200">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 mb-2 md:mb-0">
                      <span className="inline-block bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded">
                        {post.category}
                      </span>
                      <span className="text-sm text-gray-400 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {post.read_time} min
                      </span>
                      <span className="text-sm text-gray-400 flex items-center">
                        <Eye className="w-4 h-4 mr-1" />
                        {post.view_count}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDistanceToNow(new Date(post.published_at), { addSuffix: true })}
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold mb-3">
                    <Link
                      href={`/blog/${post.slug}`}
                      className="hover:text-cyan-400 transition-colors"
                    >
                      {post.title}
                    </Link>
                  </h3>

                  <p className="text-gray-300 mb-4">
                    {post.excerpt}
                  </p>

                  <Link
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center text-cyan-400 hover:text-cyan-300 text-sm"
                  >
                    Read full article <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </article>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </section>

        {/* Newsletter CTA */}
        <section className="mt-12 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
          <p className="text-lg mb-6">
            Get the latest startup insights and trending opportunities delivered to your inbox.
          </p>
          <div className="max-w-md mx-auto flex">
            <input 
              type="email" 
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-l-lg text-gray-900"
            />
            <button className="bg-gray-900 text-white px-6 py-2 rounded-r-lg hover:bg-gray-800 transition-colors">
              Subscribe
            </button>
          </div>
        </section>
      </div>
    </div>
  );
}
